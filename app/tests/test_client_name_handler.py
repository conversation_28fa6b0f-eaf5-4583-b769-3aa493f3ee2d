from unittest.mock import AsyncMock

import pytest

from constants.extracted_data import <PERSON><PERSON>tat<PERSON>
from constants.message import CLIENT_NAME_MULTIPLE_OPTIONS, CLIENT_NAME_SINGLE_CONFIRMATION
from schemas import AggregatedData, ClientSearchItem, ClientSearchResponse
from schemas.confirmed_data import ConfirmedData
from services.extracted_data.handlers import ClientNameHandler


EMPTY_MESSAGE = ''


class TestClientNameHandler:
    """Unit tests for ClientNameHandler functionality."""

    @pytest.fixture
    def client_name_handler(self):
        """Fixture providing ClientNameHandler instance."""
        return ClientNameHandler(quals_clients_repository=AsyncMock())

    @pytest.fixture
    def client_name_handler_changed_response(self):
        """Fixture providing ClientNameHandler instance with changed response."""
        # Mock the QualsClientsRepository
        mock_repository = AsyncMock()

        # Mock successful API responses for some clients
        mock_repository.search_clients.side_effect = [
            ClientSearchResponse(
                clients=[ClientSearchItem(id='1', name='Client A', qualsCount=5, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
            ),
            ClientSearchResponse(clients=[], total_count=0, page_size=5, page_idx=0),  # No results for Client B
            ClientSearchResponse(
                clients=[ClientSearchItem(id='3', name='Client C', qualsCount=3, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
            ),
        ]
        return ClientNameHandler(quals_clients_repository=mock_repository)

    @pytest.fixture
    def client_name_handler_changed_response_error(self):
        """Fixture providing ClientNameHandler instance with changed response."""
        # Mock the QualsClientsRepository
        mock_repository = AsyncMock()
        mock_repository.search_clients.side_effect = Exception('API Error')

        return ClientNameHandler(quals_clients_repository=mock_repository)

    @pytest.fixture
    def client_name_handler_changed_response_no_results(self):
        """Fixture providing ClientNameHandler instance with changed response."""
        # Mock the QualsClientsRepository
        mock_repository = AsyncMock()
        mock_repository.search_clients.side_effect = ClientSearchResponse(
            clients=[], total_count=0, page_size=5, page_idx=0
        )
        return ClientNameHandler(quals_clients_repository=mock_repository)

    @pytest.fixture
    def empty_aggregated_data(self):
        """Fixture providing empty AggregatedData."""
        return AggregatedData()

    @pytest.fixture
    def single_client_aggregated_data(self):
        """Fixture providing AggregatedData with single client name."""
        return AggregatedData(client_name=['Test Client'])

    @pytest.fixture
    def multiple_clients_aggregated_data(self):
        """Fixture providing AggregatedData with multiple client names."""
        return AggregatedData(client_name=['Client A', 'Client B', 'Client C'])

    @pytest.fixture
    def empty_confirmed_data(self):
        """Fixture providing empty ConfirmedData."""
        return ConfirmedData()

    @pytest.fixture
    def confirmed_data_with_client(self):
        """Fixture providing ConfirmedData with client name."""
        return ConfirmedData(client_name='Confirmed Client')

    async def test_client_name_already_confirmed(
        self, client_name_handler, empty_aggregated_data, confirmed_data_with_client
    ):
        """Test when client name is already confirmed - should return CONFIRMED status."""
        response = await client_name_handler.check_and_get_response(
            aggregated_data=empty_aggregated_data, confirmed_data=confirmed_data_with_client
        )

        assert response.needs_confirmation is False
        assert response.system_message is None
        assert response.next_expected_field is None
        assert response.field_status == FieldStatus.CONFIRMED

    async def test_single_client_name_needs_confirmation(
        self, client_name_handler, single_client_aggregated_data, empty_confirmed_data
    ):
        """Test when single client name is found - should return SINGLE status."""
        response = await client_name_handler.check_and_get_response(
            aggregated_data=single_client_aggregated_data, confirmed_data=empty_confirmed_data
        )

        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert 'Test Client' in response.system_message
        assert response.options == []

    async def test_multiple_client_names_without_api(
        self, client_name_handler_changed_response_no_results, multiple_clients_aggregated_data, empty_confirmed_data
    ):
        """Test when multiple client names are found without API - should return MULTIPLE status."""
        response = await client_name_handler_changed_response_no_results.check_and_get_response(
            aggregated_data=multiple_clients_aggregated_data, confirmed_data=empty_confirmed_data
        )

        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MULTIPLE
        assert response.system_message == CLIENT_NAME_MULTIPLE_OPTIONS
        assert response.options == ['Client A', 'Client B', 'Client C']

    async def test_multiple_client_names_with_api_success(
        self, client_name_handler_changed_response, multiple_clients_aggregated_data, empty_confirmed_data
    ):
        """Test when multiple client names are found with successful API search."""
        response = await client_name_handler_changed_response.check_and_get_response(
            aggregated_data=multiple_clients_aggregated_data,
            confirmed_data=empty_confirmed_data,
        )

        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.PENDING_CONFIRMATION
        assert response.system_message == CLIENT_NAME_MULTIPLE_OPTIONS
        # Should only include clients that were found by API
        assert response.options == ['Client A', 'Client C']

    async def test_no_client_names_found(self, client_name_handler, empty_aggregated_data, empty_confirmed_data):
        """Test when no client names are found - should return MISSING status."""
        response = await client_name_handler.check_and_get_response(
            aggregated_data=empty_aggregated_data, confirmed_data=empty_confirmed_data
        )

        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert 'client name' in response.system_message.lower()
        assert response.options is None

    async def test_api_error_fallback(
        self, client_name_handler_changed_response_error, multiple_clients_aggregated_data, empty_confirmed_data
    ):
        """Test when API search fails - should fallback to showing all options."""
        response = await client_name_handler_changed_response_error.check_and_get_response(
            aggregated_data=multiple_clients_aggregated_data,
            confirmed_data=empty_confirmed_data,
        )

        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MULTIPLE
        assert response.system_message == CLIENT_NAME_MULTIPLE_OPTIONS
        # Should fallback to showing all client names
        assert response.options == ['Client A', 'Client B', 'Client C']

    async def test_api_error_fallback_single_client_exception(
        self, single_client_aggregated_data, empty_confirmed_data
    ):
        """Test when API search raises exception for single client - should fallback to asking for confirmation."""
        # Create a ClientNameHandler with a repo that raises Exception
        mock_repository = AsyncMock()
        mock_repository.search_clients.side_effect = Exception('API Error')
        handler = ClientNameHandler(quals_clients_repository=mock_repository)

        response = await handler.check_and_get_response(
            aggregated_data=single_client_aggregated_data,
            confirmed_data=empty_confirmed_data,
        )

        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name='Test Client')
        assert response.options == []
