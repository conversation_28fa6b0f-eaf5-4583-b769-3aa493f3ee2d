from constants.extracted_data import Field<PERSON><PERSON>ple<PERSON><PERSON>tatus, ProgressStatus, RequiredField
from schemas import AggregatedData
from schemas.confirmed_data import ConfirmedData
from schemas.proactive_chat import FieldStatusInfo
from services.proactive_chat import ALL_FIELDS_COMPLETE_MESSAGE, ProactiveChatService


class TestProactiveChatService:
    """Test cases for ProactiveChatService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.aggregated_data = AggregatedData()
        self.confirmed_data = ConfirmedData()
        self.service = ProactiveChatService(
            self.aggregated_data,
            self.confirmed_data,
        )

    def test_get_field_status_all_missing(self):
        """Test field status when all fields are missing."""
        field_status = self.service._get_field_status()

        # Check that all fields are marked as missing
        for field in RequiredField:
            field_info = field_status[field]
            assert field_info.status == FieldCompletionStatus.MISSING
            assert field_info.value is None
            assert field_info.display_name is not None

    def test_get_field_status_all_completed(self):
        """Test field status when all fields are completed."""
        self.confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective and scope',
            outcomes='Test outcomes',
        )
        self.service = ProactiveChatService(self.aggregated_data, self.confirmed_data)
        field_status = self.service._get_field_status()

        # Check that all fields are marked as completed
        for field in RequiredField:
            field_info = field_status[field]
            assert field_info.status == FieldCompletionStatus.COMPLETED
            assert field_info.value is not None
            assert field_info.display_name is not None

    def test_get_field_status_pending_confirmation(self):
        """Test field status when fields need confirmation."""
        self.aggregated_data = AggregatedData(
            client_name=['Client A', 'Client B'], ldmf_country=['United States', 'Canada']
        )
        self.service = ProactiveChatService(self.aggregated_data, self.confirmed_data)
        field_status = self.service._get_field_status()

        # Check that fields with aggregated data are pending confirmation
        assert field_status[RequiredField.CLIENT_INFO].status == FieldCompletionStatus.PENDING_CONFIRMATION
        assert field_status[RequiredField.LDMF_COUNTRY].status == FieldCompletionStatus.PENDING_CONFIRMATION
        assert field_status[RequiredField.ENGAGEMENT_DATES].status == FieldCompletionStatus.MISSING
        assert field_status[RequiredField.OBJECTIVE_SCOPE].status == FieldCompletionStatus.MISSING
        assert field_status[RequiredField.OUTCOMES].status == FieldCompletionStatus.MISSING

    def test_get_next_required_field(self):
        """Test getting the next required field."""
        field_status = {
            RequiredField.CLIENT_INFO: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value='Test Client', display_name='Client Name'
            ),
            RequiredField.LDMF_COUNTRY: FieldStatusInfo(
                status=FieldCompletionStatus.MISSING, value=None, display_name='Lead Deloitte Member Firm Country'
            ),
            RequiredField.ENGAGEMENT_DATES: FieldStatusInfo(
                status=FieldCompletionStatus.MISSING, value=None, display_name='Engagement Dates'
            ),
            RequiredField.OBJECTIVE_SCOPE: FieldStatusInfo(
                status=FieldCompletionStatus.MISSING, value=None, display_name='Objective and Scope'
            ),
            RequiredField.OUTCOMES: FieldStatusInfo(
                status=FieldCompletionStatus.MISSING, value=None, display_name='Outcomes'
            ),
        }

        next_field = self.service._get_next_required_field(field_status)
        assert next_field == RequiredField.LDMF_COUNTRY

    def test_get_next_required_field_all_completed(self):
        """Test getting next field when all are completed."""
        field_status = {
            RequiredField.CLIENT_INFO: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value='Test Client', display_name='Client Name'
            ),
            RequiredField.LDMF_COUNTRY: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED,
                value='United States',
                display_name='Lead Deloitte Member Firm Country',
            ),
            RequiredField.ENGAGEMENT_DATES: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED,
                value='2023-01-01 to 2023-12-31',
                display_name='Engagement Dates',
            ),
            RequiredField.OBJECTIVE_SCOPE: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value='Test objective', display_name='Objective and Scope'
            ),
            RequiredField.OUTCOMES: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value='Test outcomes', display_name='Outcomes'
            ),
        }

        next_field = self.service._get_next_required_field(field_status)
        assert next_field is None

    def test_get_completion_progress(self):
        """Test completion progress calculation."""
        field_status = {
            RequiredField.CLIENT_INFO: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value='Test Client', display_name='Client Name'
            ),
            RequiredField.LDMF_COUNTRY: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED,
                value='United States',
                display_name='Lead Deloitte Member Firm Country',
            ),
            RequiredField.ENGAGEMENT_DATES: FieldStatusInfo(
                status=FieldCompletionStatus.PENDING_CONFIRMATION,
                value='2023-01-01 to 2023-12-31',
                display_name='Engagement Dates',
            ),
            RequiredField.OBJECTIVE_SCOPE: FieldStatusInfo(
                status=FieldCompletionStatus.MISSING, value=None, display_name='Objective and Scope'
            ),
            RequiredField.OUTCOMES: FieldStatusInfo(
                status=FieldCompletionStatus.MISSING, value=None, display_name='Outcomes'
            ),
        }

        progress = self.service._get_completion_progress(field_status)

        assert progress.total == 5
        assert progress.completed == 2
        assert progress.pending_confirmation == 1
        assert progress.missing == 2
        assert progress.percentage == 40
        assert progress.status == ProgressStatus.IN_PROGRESS

    def test_get_completion_progress_all_completed(self):
        """Test completion progress when all fields are completed."""
        field_status = {
            RequiredField.CLIENT_INFO: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value='Test Client', display_name='Client Name'
            ),
            RequiredField.LDMF_COUNTRY: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED,
                value='United States',
                display_name='Lead Deloitte Member Firm Country',
            ),
            RequiredField.ENGAGEMENT_DATES: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED,
                value='2023-01-01 to 2023-12-31',
                display_name='Engagement Dates',
            ),
            RequiredField.OBJECTIVE_SCOPE: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value='Test objective', display_name='Objective and Scope'
            ),
            RequiredField.OUTCOMES: FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value='Test outcomes', display_name='Outcomes'
            ),
        }

        progress = self.service._get_completion_progress(field_status)

        assert progress.total == 5
        assert progress.completed == 5
        assert progress.pending_confirmation == 0
        assert progress.missing == 0
        assert progress.percentage == 100
        assert progress.status == ProgressStatus.COMPLETED

    def test_enrich_system_message_with_status_update_missing_fields(self):
        """Test system message enrichment with missing fields."""

        enriched_message = self.service.get_enriched_system_message()

        assert 'Next: We need to collect the Client Name' in enriched_message
        assert 'Please provide the client name for this engagement' in enriched_message

    def test_enrich_system_message_with_status_update_all_completed(self):
        """Test system message enrichment when all fields are complete."""

        # Set up completed data
        self.confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective and scope',
            outcomes='Test outcomes',
        )
        self.service = ProactiveChatService(self.aggregated_data, self.confirmed_data)

        enriched_message = self.service.get_enriched_system_message()

        field_summary = (
            '✅ Client Name'
            '\n✅ Lead Deloitte Member Firm Country'
            '\n✅ Engagement Dates'
            '\n✅ Objective and Scope'
            '\n✅ Outcomes'
        )
        assert enriched_message == ALL_FIELDS_COMPLETE_MESSAGE.format(field_summary=field_summary)

    def test_enrich_system_message_with_status_update_partial_fields_completed(self):
        """Test system message enrichment when some of the fields are complete."""

        # Set up completed data
        self.confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope=None,
            outcomes=None,
        )
        self.service = ProactiveChatService(
            aggregated_data=self.aggregated_data,
            confirmed_data=self.confirmed_data,
            add_progress_information=True,
            add_remaining_fields_summary=True,
        )

        enriched_message = self.service.get_enriched_system_message()

        assert enriched_message == (
            '\n\nProgress: 3/5 fields completed (60%)'
            '\n\nNext: We need to collect the Objective and Scope. Please describe the objective and scope of this engagement.'
            "\n\nRemaining fields: ['Objective and Scope', 'Outcomes']. "
        )
