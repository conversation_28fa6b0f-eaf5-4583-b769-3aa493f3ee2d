from typing import Sequence
from uuid import UUI<PERSON>

from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from constants.extracted_data import DataSourceType
from exceptions import EntityNotFoundError
from models.qual_conversation import ConversationState, QualConversation
from models.qual_extracted_data import QualExtractedData
from schemas import ExtractedData

from .conversation import ConversationRepository


__all__ = ['ExtractedDataRepository']


class ExtractedDataRepository:
    """Repository for extracted data-related database operations."""

    def __init__(self, db_session: AsyncSession, conversation_repository: ConversationRepository):
        self.db_session = db_session
        self.conversation_repository = conversation_repository

    async def create(self, conversation_id: UUID, data_source_type: DataSourceType):
        """
        Create a new extracted data record.

        Args:
            conversation_id: The ID of the conversation
            data_source_type: The type of source data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        extracted_data = QualExtractedData(
            QualConversationId=conversation_internal_id,
            DataSourceType=data_source_type,
        )
        self.db_session.add(extracted_data)
        await self.db_session.flush()

    async def get(self, conversation_id: UUID, data_source_type: DataSourceType) -> ExtractedData | None:
        """
        Get extracted data.

        Args:
            conversation_id: Public conversation ID
            data_source_type: Type of source data to retrieve

        Returns:
            The extracted data if found, None otherwise
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        result = (
            await self.db_session.execute(
                select(QualExtractedData).where(
                    QualExtractedData.QualConversationId == conversation_internal_id,
                    QualExtractedData.DataSourceType == data_source_type,
                )
            )
        ).scalar_one_or_none()
        if not result:
            return None

        result.ConversationPublicId = conversation_id
        return ExtractedData.model_validate(result)

    async def list_dash_tasks(
        self,
        qual_created: bool = False,
        activity_ids: Sequence[int] | None = None,
    ) -> Sequence[ExtractedData]:
        """
        Get all extracted data for a given data source type.

        Args:
            data_source_type: The type of data source to retrieve
            activity_ids: Optional list of activity IDs to filter by

        Returns:
            A list of ExtractedData objects
        """
        query = (
            select(QualExtractedData)
            .options(selectinload(QualExtractedData.Conversation))
            .where(QualExtractedData.DataSourceType == DataSourceType.KX_DASH)
        )

        if qual_created:
            query = query.join(QualConversation).where(QualConversation.State == ConversationState.QUAL_CREATED)

        if activity_ids:
            query = query.where(QualExtractedData.ActivityId.in_(activity_ids))

        result = await self.db_session.execute(query)
        extracted_data_list = result.scalars().all()

        for extracted_data in extracted_data_list:
            extracted_data.conversation_id = extracted_data.Conversation.PublicId

        return tuple(ExtractedData.model_validate(extracted_data) for extracted_data in extracted_data_list)

    async def update(self, extracted_data: ExtractedData):
        """
        Upsert extracted data of a specified source type for a conversation.
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(extracted_data.conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(extracted_data.conversation_id))

        extracted_data_db = (
            await self.db_session.execute(
                select(QualExtractedData).where(
                    QualExtractedData.QualConversationId == conversation_internal_id,
                    QualExtractedData.DataSourceType == extracted_data.data_source_type,
                )
            )
        ).scalar_one_or_none()

        if extracted_data_db:
            for key, val in extracted_data.model_dump_for_db(exclude_none=True).items():
                setattr(extracted_data_db, key, val)
        else:
            extracted_data_db = QualExtractedData(
                QualConversationId=conversation_internal_id, **extracted_data.model_dump_for_db()
            )
            self.db_session.add(extracted_data_db)

        await self.db_session.flush()

    async def delete(self, conversation_id: UUID, data_source_type: DataSourceType) -> None:
        """
        Delete extracted data of a specified source type for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data for.
            data_source_type: The type of data source to delete extracted data for.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        query = delete(QualExtractedData).where(
            QualExtractedData.QualConversationId == conversation_internal_id,
            QualExtractedData.DataSourceType == data_source_type,
        )
        await self.db_session.execute(query)
        await self.db_session.flush()

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all extracted data associated with a conversation.

        Args:
            conversation_id: The ID of the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        query = delete(QualExtractedData).where(QualExtractedData.QualConversationId == conversation_internal_id)
        await self.db_session.execute(query)
        await self.db_session.flush()
