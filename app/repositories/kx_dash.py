from datetime import datetime
import logging
from typing import Any, Dict, List

from config import settings
from constants.environment import Environment
from core.urls import url_join
from dependencies import CustomAsyncClient
from exceptions.kx_dash import KXDashDateParsingError


__all__ = ['KXDashRepository']


logger = logging.getLogger(__name__)


class KXDashRepository:
    """Repository for KX Dash API operations."""

    MOCKED_TASKS = {
        100001: {
            'activityId': 100001,
            'activityName': 'Quality Review - Late Due Date',
            'clientName': 'TechCorp Industries',
            'memberFirm': 'US',
            'country': 'United States',
            'globalBusiness': 'Audit & Assurance',
            'globalBusinessServiceArea': 'Audit',
            'globalBusinessServiceLine': 'External Audit',
            'globalIndustry': 'Technology, Media & Telecommunications',
            'globalIndustrySector': 'Software & Computer Services',
            'engagementCode': 'ENG-100001',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': '2024-01-15',
            'engagementEndDate': '2024-12-31',
            'dueDate': '2024-12-30',  # Latest due date
        },
        100002: {
            'activityId': 100002,
            'activityName': 'Risk Assessment - Middle Due Date',
            'clientName': 'Global Financial Solutions Ltd',
            'memberFirm': 'UK',
            'country': 'United Kingdom',
            'globalBusiness': 'Risk Advisory',
            'globalBusinessServiceArea': 'Risk & Financial Advisory',
            'globalBusinessServiceLine': 'Regulatory & Legal Support',
            'globalIndustry': 'Financial Services',
            'globalIndustrySector': 'Investment Banking',
            'engagementCode': 'ENG-100002',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': '2024-03-01',
            'engagementEndDate': '2024-09-30',
            'dueDate': '2024-06-15',  # Middle due date
        },
        100003: {
            'activityId': 100003,
            'activityName': 'Compliance Review - Should be Filtered',
            'clientName': 'Manufacturing Corp',
            'memberFirm': 'CA',
            'country': 'Canada',
            'globalBusiness': 'Audit & Assurance',
            'globalBusinessServiceArea': 'Audit',
            'globalBusinessServiceLine': 'Internal Audit',
            'globalIndustry': 'Manufacturing',
            'globalIndustrySector': 'Industrial Manufacturing',
            'engagementCode': 'ENG-100003',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': '2024-02-01',
            'engagementEndDate': '2024-11-30',
            'dueDate': '2024-05-01',  # Early due date - will be filtered out
        },
        100004: {
            'activityId': 100004,
            'activityName': 'Tax Advisory - Earliest Due Date',
            'clientName': 'Retail Chain Ltd',
            'memberFirm': 'AU',
            'country': 'Australia',
            'globalBusiness': 'Tax & Legal',
            'globalBusinessServiceArea': 'Tax',
            'globalBusinessServiceLine': 'Tax Advisory',
            'globalIndustry': 'Consumer Products',
            'globalIndustrySector': 'Retail',
            'engagementCode': 'ENG-100004',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': '2024-01-01',
            'engagementEndDate': '2024-05-31',
            'dueDate': '2024-04-15',  # Earliest due date
        },
        100005: {
            'activityId': 100005,
            'activityName': 'Strategy Consulting - No Due Date',
            'clientName': 'European Consulting Group',
            'memberFirm': 'DE',
            'country': 'Germany',
            'globalBusiness': 'Consulting',
            'globalBusinessServiceArea': 'Strategy & Operations',
            'globalBusinessServiceLine': 'Strategy',
            'globalIndustry': 'Energy & Resources',
            'globalIndustrySector': 'Oil & Gas',
            'engagementCode': 'ENG-100005',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': '2024-06-01',
            'engagementEndDate': '2024-12-31',
            'dueDate': None,  # No due date - should be sorted last
        },
    }

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the KX Dash Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = settings.kx_dash_api.base_url

    async def list(self, token: str) -> List[Dict[str, Any]]:
        """
        Get list of activities from KX Quals API.

        Returns:
            list[dict[str, Any]]: List of activities in dash format
        """
        if settings.environment == Environment.LOCAL:
            return list(self.MOCKED_TASKS.values())

        url = url_join(self._base_path, 'getMyFilteredTasks')
        headers = {'Authorization': f'Bearer {token}'}
        response = (await self._http_client.get(url, headers=headers)).json()
        logger.info('Result: %s', response)  # TODO: remove this logging
        return self._parse_kx_dash_response(response)

    async def get(self, activity_id: int, token: str) -> Dict[str, Any] | None:
        """
        Get a specific activity from KX Quals API.

        Args:
            activity_id: The ID of the activity to retrieve

        Returns:
            dict[str, Any] | None: Activity data in dash format or None if not found
        """
        if settings.environment == Environment.LOCAL:
            return self.MOCKED_TASKS.get(activity_id)

        url = url_join(self._base_path, 'getActivityDataById')
        headers = {'Authorization': f'Bearer {token}'}
        response = (await self._http_client.get(url, params={'activityId': activity_id}, headers=headers)).json()
        return response or None

    @staticmethod
    def _parse_date_string(date_string: str | None) -> str | None:
        """
        Parse date string from API response and convert to date format.

        Args:
            date_string: Date string in format '2025-04-19T00:00:00Z' or '2025-04-19'

        Returns:
            Date string in format '2025-04-19' or None
        """
        if not date_string:
            return None

        # Parse datetime string and extract date part
        try:
            # Handle Z timezone suffix
            clean_date_string = date_string.replace('Z', '+00:00')
            parsed_datetime = datetime.fromisoformat(clean_date_string)
            return parsed_datetime.date().isoformat()
        except Exception:
            raise KXDashDateParsingError(date_string)

    def _parse_kx_dash_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Parse KX Quals API response and convert it to dash task format.

        Args:
            response: Raw response from KX Quals API

        Returns:
            list[dict[str, Any]]: List of tasks in dash format
        """
        if not response:
            return []

        tasks = []
        for item in response.get('data', []):
            # Extract owner information
            owners = item.get('owner', {}).get('value', [])
            owner_emails = [owner.get('email') for owner in owners if owner.get('email')]

            # Extract engagement information
            engagement = item.get('engagement', {})
            engagement_lep = engagement.get('lep', {}).get('value', [])
            lep_emails = [lep.get('email') for lep in engagement_lep if lep.get('email')]
            engagement_manager = engagement.get('manager', {}).get('value', [])
            manager_emails = [manager.get('email') for manager in engagement_manager if manager.get('email')]

            task = {
                'activityId': item.get('id', {}).get('value'),
                'activityName': item.get('activityName', {}).get('value'),
                'clientName': item.get('clientNameLocal', {}).get('value'),
                'memberFirm': item.get('memberFirm', {}).get('value'),
                'country': item.get('country', {}).get('value'),
                'globalBusiness': item.get('globalBusiness', {}).get('value'),
                'globalBusinessServiceArea': item.get('globalBusinessServiceArea', {}).get('value'),
                'globalBusinessServiceLine': item.get('globalBusinessServiceLine', {}).get('value'),
                'globalIndustry': item.get('globalIndustry', {}).get('value'),
                'globalIndustrySector': item.get('globalIndustrySector', {}).get('value'),
                'engagementCode': engagement.get('code', {}).get('value'),
                'globalLCSPEmails': item.get('globalLCSPEmails', {}).get('value', []),
                'engagementLepEmails': lep_emails,
                'engagementManagerEmails': manager_emails,
                'activityOwnerEmails': owner_emails,
                'engagementStartDate': self._parse_date_string(engagement.get('startDate', {}).get('value')),
                'engagementEndDate': self._parse_date_string(engagement.get('endDate', {}).get('value')),
                'dueDate': self._parse_date_string(item.get('dueDate', {}).get('value')),
            }
            tasks.append(task)

        return tasks
