"""
Document Queue Repository for Durable Functions.

This is a version of the DocumentQueueRepository that doesn't import the main
FastAPI application settings, avoiding the KeyError for SIGNAL_R_CONNECTION_STRING.
"""

import json
import logging
from typing import Any, Optional
from uuid import UUID

from azure.core.exceptions import ResourceExistsError
from azure.storage.queue import BinaryBase64DecodePolicy, BinaryBase64EncodePolicy, QueueMessage
from azure.storage.queue.aio import QueueClient

from durable_functions.application.config import settings


__all__ = ['DocumentQueueRepository']

logger = logging.getLogger(__name__)


class DocumentQueueRepository:
    """Document queue repository for durable functions context."""

    def __init__(self, connection_string: str, queue_name: str):
        self.connection_string = connection_string
        self.queue_name = queue_name
        self.queue_client = self._create_queue_client(queue_name)

    def _create_queue_client(self, queue_name: str) -> QueueClient:
        return QueueClient.from_connection_string(
            self.connection_string,
            queue_name,
            message_encode_policy=BinaryBase64EncodePolicy(),
            message_decode_policy=BinaryBase64DecodePolicy(),
        )

    async def _ensure_queue_exists(self, queue_client: QueueClient, queue_name: str) -> None:
        try:
            await queue_client.create_queue()
            logger.debug("Queue '%s' created", queue_name)
        except ResourceExistsError:
            logger.debug("Queue '%s' already exists", queue_name)

    async def _send_message(self, content: dict[str, Any], queue_name: Optional[str] = None) -> QueueMessage:
        target_queue_name = queue_name or self.queue_name
        queue_client = (
            self.queue_client if target_queue_name == self.queue_name else self._create_queue_client(target_queue_name)
        )
        await self._ensure_queue_exists(queue_client, target_queue_name)

        message_bytes = json.dumps(content).encode('utf-8')
        try:
            msg = await queue_client.send_message(message_bytes)
            logger.info("Message sent to queue '%s': %s", target_queue_name, content)
            return msg
        except Exception:
            logger.exception("Error sending message to queue '%s'", target_queue_name)
            raise

    async def send_document_message(self, blob_url: str, signalr_user_id: UUID) -> QueueMessage:
        content = {'blob_url': blob_url, 'signalr_user_id': str(signalr_user_id)}
        # Use durable functions settings instead of main app settings
        return await self._send_message(content, queue_name=settings.QUEUE_SETTINGS.DOCUMENT_PROCESSING_QUEUE)

    async def send_prompt_message(self, prompt_url: str, signalr_user_id: UUID) -> QueueMessage:
        content = {'prompt_url': prompt_url, 'signalr_user_id': str(signalr_user_id)}
        # Use durable functions settings instead of main app settings
        return await self._send_message(content, queue_name=settings.QUEUE_SETTINGS.PROMPT_PROCESSING_QUEUE)

    async def send_message(self, message_content: dict[str, Any]) -> QueueMessage:
        """
        Send a message to the default queue.
        """
        return await self._send_message(message_content)
