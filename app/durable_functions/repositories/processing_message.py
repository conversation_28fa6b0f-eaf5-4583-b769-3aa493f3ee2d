from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased

from models import QualConversationMessage, QualProcessingMessage
from schemas import ProcessingStatusUpdatePayload


__all__ = ['ProcessingMessageRepository']


class ProcessingMessageRepository:
    """
    A repository class to abstract database operations for file processing messages.
    Currently uses an in-memory dictionary to simulate database interactions.
    """

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create(self, data: ProcessingStatusUpdatePayload) -> QualProcessingMessage:
        """
        Create the processing status of a message.

        Args:
            message_id: The ID of the message
            status: The new status
            message: Optional message to include
            metadata: Optional metadata to include

        Returns:
            The created message data
        """
        message_alias = aliased(QualConversationMessage, name='msg')
        query = select(message_alias.Id).where(message_alias.PublicId == data.message_id)
        result = await self.db_session.execute(query)
        message_id = result.scalar_one()

        new_status = QualProcessingMessage(
            QualConversationMessageId=message_id,
            Status=data.status,
            Message=data.message,
            Metadata=data.metadata,
        )

        self.db_session.add(new_status)
        await self.db_session.flush()

        new_status.MessagePublicId = data.message_id

        return new_status
