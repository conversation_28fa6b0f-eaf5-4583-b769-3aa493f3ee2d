import logging

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from constants.extracted_data import ConversationState
from durable_functions.utils.exceptions.entity import EntityNotFoundError
from models.qual_extracted_data import QualExtractedData
from schemas import ExtractedData

from .conversation import ConversationRepository


__all__ = ['ExtractedDataRepository']
logger = logging.getLogger(__name__)


class ExtractedDataRepository:
    """Repository for extracted data-related database operations."""

    def __init__(self, db_session: AsyncSession, conversation_repository: ConversationRepository):
        self.db_session = db_session
        self.conversation_repository = conversation_repository

    async def update(self, extracted_data: ExtractedData):
        """
        Upsert extracted data of a specified source type for a conversation.
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(extracted_data.conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(extracted_data.conversation_id))

        extracted_data_db = (
            await self.db_session.execute(
                select(QualExtractedData).where(
                    QualExtractedData.QualConversationId == conversation_internal_id,
                    QualExtractedData.DataSourceType == extracted_data.data_source_type,
                )
            )
        ).scalar_one_or_none()

        if extracted_data_db:
            for key, val in extracted_data.model_dump_for_db(exclude_none=True).items():
                setattr(extracted_data_db, key, val)
        else:
            extracted_data_db = QualExtractedData(
                QualConversationId=conversation_internal_id, **extracted_data.model_dump_for_db()
            )
            self.db_session.add(extracted_data_db)

        confirmed_data = await self.conversation_repository.get_confirmed_data(extracted_data.conversation_id)

        if extracted_data.required_fields_are_complete:
            if not extracted_data.ldmf_country or not extracted_data.start_date or not extracted_data.end_date:
                logger.warning(
                    'Required fields check passed but essential fields are missing for conversation %s',
                    extracted_data.conversation_id,
                )
                return

            confirmed_data.ldmf_country = extracted_data.ldmf_country[0]
            confirmed_data.date_intervals = (
                extracted_data.start_date.isoformat(),
                extracted_data.end_date.isoformat(),
            )
            confirmed_data.objective_and_scope = extracted_data.objective_and_scope
            confirmed_data.outcomes = extracted_data.outcomes

        # if some fields are valid
        else:
            if extracted_data.is_ldmf_country_complete:
                confirmed_data.ldmf_country = extracted_data.ldmf_country[0]
            if extracted_data.is_date_interval_complete:
                confirmed_data.date_intervals = (
                    extracted_data.start_date.isoformat(),
                    extracted_data.end_date.isoformat(),
                )  # type: ignore
            if extracted_data.is_objective_and_scope_complete:
                confirmed_data.objective_and_scope = extracted_data.objective_and_scope
            if extracted_data.is_outcomes_complete:
                confirmed_data.outcomes = extracted_data.outcomes

        await self.conversation_repository.update_confirmed_data_and_state(
            public_id=extracted_data.conversation_id,
            confirmed_data=confirmed_data,
            state=ConversationState.COLLECTING_CLIENT_NAME,
        )

        await self.db_session.flush()
