import json
import logging

import azure.durable_functions as df
from azure.functions import QueueMessage

from durable_functions.application.config import settings
from durable_functions.utils import OrchestratorInputType, OrchestratorName


logger = logging.getLogger(__name__)

QUEUE_NAME = settings.QUEUE_SETTINGS.DOCUMENT_PROCESSING_QUEUE
PROMPT_PROCESSING_QUEUE = settings.QUEUE_SETTINGS.PROMPT_PROCESSING_QUEUE
CONNECTION_ENV = settings.QUEUE_SETTINGS.CONNECTION_ENV

bp = df.Blueprint()


@bp.queue_trigger(arg_name='msg', queue_name=QUEUE_NAME, connection=CONNECTION_ENV)
@bp.durable_client_input(client_name='client')
async def process_document_queue(msg: QueueMessage, client: df.DurableOrchestrationClient) -> None:
    """
    Queue trigger to start document processing.

    Expected message format:
    {
        "blob_url": "https://...",
        "signalr_user_id": "user-uuid" (optional)
    }

    Args:
        msg: Queue message
        client: Durable orchestration client

    Returns:
        None
    """
    try:
        msg_body = msg.get_body().decode('utf-8')
        msg_json = json.loads(msg_body)
        logger.info(f'Received queue message: {msg_json}')

        blob_url = msg_json['blob_url']
        signalr_user_id = msg_json['signalr_user_id']

        orchestrator_input = {
            'blob_url': blob_url,
            'type': OrchestratorInputType.Document,
            'signalr_user_id': signalr_user_id,
        }
        orchestrator_name = OrchestratorName.DocumentProcessing

        logger.info(f'Starting orchestrator: {orchestrator_name} with input: {orchestrator_input}')
        instance_id = await client.start_new(orchestrator_name, None, orchestrator_input)
        logger.info(f'Started orchestration instance: {instance_id}')

    except Exception as e:
        logger.exception(f'Error processing queue message: {e}')


@bp.queue_trigger(arg_name='msg', queue_name=PROMPT_PROCESSING_QUEUE, connection=CONNECTION_ENV)
@bp.durable_client_input(client_name='client')
async def process_prompt_queue(msg: QueueMessage, client: df.DurableOrchestrationClient) -> None:
    """
    Queue trigger to start prompt processing.

    Expected message format:
    {
        "prompt_url": "https://...",
        "signalr_user_id": "user-uuid" (optional)
    }

    Args:
        msg: Queue message
        client: Durable orchestration client

    Returns:
        None
    """
    try:
        msg_body = msg.get_body().decode('utf-8')
        msg_json = json.loads(msg_body)
        logger.info(f'Received queue message: {msg_json}')

        prompt_url = msg_json['prompt_url']
        signalr_user_id = msg_json['signalr_user_id']

        orchestrator_input = {
            'prompt_url': prompt_url,
            'type': OrchestratorInputType.Prompt,
            'signalr_user_id': signalr_user_id,
        }
        orchestrator_name = OrchestratorName.DocumentProcessing

        logger.info(f'Starting orchestrator: {orchestrator_name} with input: {orchestrator_input}')
        instance_id = await client.start_new(orchestrator_name, None, orchestrator_input)
        logger.info(f'Started orchestration instance: {instance_id}')

    except Exception as e:
        logger.exception(f'Error processing queue message: {e}')
