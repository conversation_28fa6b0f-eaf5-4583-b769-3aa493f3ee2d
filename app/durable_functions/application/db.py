from typing import cast

from sqlalchemy.engine.base import Engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from durable_functions.application.config import settings


__all__ = ['async_session_local']


async_engine = create_async_engine(settings.db.uri, echo=False, pool_pre_ping=True)

async_session_local = sessionmaker(
    bind=cast(Engine, async_engine),
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)
