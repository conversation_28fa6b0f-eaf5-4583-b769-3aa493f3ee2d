from dataclasses import dataclass
import logging

from constants.extracted_data import FieldCompletionStatus, ProgressStatus, RequiredField
from schemas import AggregatedData
from schemas.confirmed_data import ConfirmedData
from schemas.proactive_chat import FieldStatusInfo, ProgressInfo


__all__ = ['ProactiveChatService']

logger = logging.getLogger(__name__)


# New message for when all required fields are complete
ALL_FIELDS_COMPLETE_MESSAGE = """Excellent! All required information has been collected.

Your qual is ready to be generated with the following confirmed details:

{field_summary}

You can now proceed to generate your qual. Is there anything else you'd like to add or modify before we create the final qual?"""


@dataclass(frozen=True)
class ProactiveChatService:
    """Service for proactive chat functionality that guides users through required field collection."""

    _FIELD_COLLECTION_ORDER = [
        RequiredField.CLIENT_INFO,
        RequiredField.LDMF_COUNTRY,
        RequiredField.ENGAGEMENT_DATES,
        RequiredField.OBJECTIVE_SCOPE,
        RequiredField.OUTCOMES,
    ]
    _REQUIRED_FIELDS = {
        RequiredField.CLIENT_INFO: 'Client Name',
        RequiredField.LDMF_COUNTRY: 'Lead Deloitte Member Firm Country',
        RequiredField.ENGAGEMENT_DATES: 'Engagement Dates',
        RequiredField.OBJECTIVE_SCOPE: 'Objective and Scope',
        RequiredField.OUTCOMES: 'Outcomes',
    }
    _REQUIRED_FIELDS_GUIDANCE_MAP = {
        RequiredField.CLIENT_INFO: 'Please provide the client name for this engagement.',
        RequiredField.LDMF_COUNTRY: 'Please specify the Lead Deloitte Member Firm country.',
        RequiredField.ENGAGEMENT_DATES: 'Please provide the engagement start and end dates.',
        RequiredField.OBJECTIVE_SCOPE: 'Please describe the objective and scope of this engagement.',
        RequiredField.OUTCOMES: 'Please describe the outcomes and results of this engagement.',
    }

    _TEXT_FOR_PROGRESS = '\n\nProgress: {progress.completed}/{progress.total} fields completed ({progress.percentage}%)'
    _TEXT_FOR_MISSING_NEXT_FIELD = '\n\nNext: We need to collect the {next_field_info.display_name}. '
    _TEXT_FOR_PENDING_CONFIRMATION_NEXT_FIELD = (
        '\n\nConfirm: Please confirm the {next_field_info.display_name}: {value}. '
    )
    _TEXT_FOR_REMAINING_FIELDS = '\n\nRemaining fields: {remaining_fields}. '

    aggregated_data: AggregatedData
    confirmed_data: ConfirmedData

    add_progress_information: bool = False
    add_next_field_guidance: bool = True
    add_remaining_fields_summary: bool = False

    def get_enriched_system_message(self) -> str:
        """
        Generate a proactive message that guides the user.
        """

        field_status = self._get_field_status()
        next_field = self._get_next_required_field(field_status)
        progress = self._get_completion_progress(field_status)

        # Check if all fields are complete
        if progress.status == ProgressStatus.COMPLETED:
            # All fields are complete - provide a special completion message
            enhanced_message_content = self._get_all_fields_complete_msg(field_status)
            return enhanced_message_content

        enhanced_message_content = ''

        if self.add_progress_information:
            # Add progress information
            enhanced_message_content += self._TEXT_FOR_PROGRESS.format(progress=progress)

        if next_field and self.add_next_field_guidance:
            # Add next steps guidance
            next_field_info = field_status[next_field]

            if next_field_info.status == FieldCompletionStatus.MISSING:
                next_steps = self._TEXT_FOR_MISSING_NEXT_FIELD.format(
                    next_field_info=next_field_info,
                )
                next_steps += self._REQUIRED_FIELDS_GUIDANCE_MAP[next_field]
                enhanced_message_content += next_steps

            elif next_field_info.status == FieldCompletionStatus.PENDING_CONFIRMATION:
                enhanced_message_content += self._TEXT_FOR_PENDING_CONFIRMATION_NEXT_FIELD.format(
                    next_field_info=next_field_info,
                    value=next_field_info.value or '',
                )
            elif next_field_info.status == FieldCompletionStatus.COMPLETED:
                pass
            else:
                raise NotImplementedError(f'Field status: {next_field_info.status} is not supported.')

        if self.add_remaining_fields_summary:
            # Add remaining fields summary
            missing_fields = [
                field for field, info in field_status.items() if info.status == FieldCompletionStatus.MISSING
            ]
            if len(missing_fields) > 1:
                enhanced_message_content += self._TEXT_FOR_REMAINING_FIELDS.format(
                    remaining_fields=[field_status[field].display_name for field in missing_fields],
                )

        return enhanced_message_content

    # required fields checkers

    def _get_field_status(self) -> dict[RequiredField, FieldStatusInfo]:
        """Get the status of all required fields."""
        return {
            RequiredField.CLIENT_INFO: self._check_field_status(
                confirmed_value=self.confirmed_data.client_name,
                aggregated_value=self.aggregated_data.client_name,
                display_name=self._REQUIRED_FIELDS[RequiredField.CLIENT_INFO],
            ),
            RequiredField.LDMF_COUNTRY: self._check_field_status(
                confirmed_value=self.confirmed_data.ldmf_country,
                aggregated_value=self.aggregated_data.ldmf_country,
                display_name=self._REQUIRED_FIELDS[RequiredField.LDMF_COUNTRY],
            ),
            RequiredField.ENGAGEMENT_DATES: self._check_field_status(
                confirmed_value=self._format_date_intervals(self.confirmed_data.date_intervals),
                aggregated_value=self._format_list_of_date_intervals(self.aggregated_data.date_intervals),
                display_name=self._REQUIRED_FIELDS[RequiredField.ENGAGEMENT_DATES],
            ),
            RequiredField.OBJECTIVE_SCOPE: self._check_field_status(
                confirmed_value=self.confirmed_data.objective_and_scope,
                aggregated_value=self.aggregated_data.objective_and_scope,
                display_name=self._REQUIRED_FIELDS[RequiredField.OBJECTIVE_SCOPE],
            ),
            RequiredField.OUTCOMES: self._check_field_status(
                confirmed_value=self.confirmed_data.outcomes,
                aggregated_value=self.aggregated_data.outcomes,
                display_name=self._REQUIRED_FIELDS[RequiredField.OUTCOMES],
            ),
        }

    def _format_date_intervals(self, date_intervals: tuple[str | None, str | None] | None) -> str | None:
        """Format date intervals as a readable string."""
        if not date_intervals or len(date_intervals) != 2:
            return None

        start_date: str | None = date_intervals[0]
        end_date: str | None = date_intervals[1]

        if not start_date or not end_date:
            return None

        return f'{start_date} to {end_date}'

    def _format_list_of_date_intervals(self, date_intervals: list[tuple[str | None, str | None]] | None) -> str | None:
        """Format list of date intervals as a readable string."""
        if not date_intervals:
            return None

        formatted_date_intervals = []
        for date_interval in date_intervals:
            formatted_date_interval = self._format_date_intervals(date_interval)
            if not formatted_date_interval:
                continue
            formatted_date_intervals.append(formatted_date_interval)

        if not formatted_date_intervals:
            return None

        return ', '.join(formatted_date_intervals)

    def _check_field_status(
        self, confirmed_value: str | list | None, aggregated_value: str | list | None, display_name: str
    ) -> FieldStatusInfo:
        """Helper method to check field status based on confirmed and aggregated values."""
        if confirmed_value:
            return FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED, value=confirmed_value, display_name=display_name
            )
        elif aggregated_value:
            return FieldStatusInfo(
                status=FieldCompletionStatus.PENDING_CONFIRMATION, value=aggregated_value, display_name=display_name
            )
        else:
            return FieldStatusInfo(status=FieldCompletionStatus.MISSING, value=None, display_name=display_name)

    # progress status checkers

    def _get_next_required_field(self, field_status: dict[RequiredField, FieldStatusInfo]) -> RequiredField | None:
        """Get the next required field that needs attention."""
        for field in self._FIELD_COLLECTION_ORDER:
            field_info = field_status[field]
            if field_info.status not in [
                FieldCompletionStatus.COMPLETED,
            ]:
                return field
        return None

    def _get_completion_progress(self, field_status: dict[RequiredField, FieldStatusInfo]) -> ProgressInfo:
        """Get completion progress information."""
        total_fields = len(self._REQUIRED_FIELDS)
        completed_fields = sum(1 for info in field_status.values() if info.status == FieldCompletionStatus.COMPLETED)
        pending_fields = sum(
            1 for info in field_status.values() if info.status == FieldCompletionStatus.PENDING_CONFIRMATION
        )
        missing_fields = sum(1 for info in field_status.values() if info.status == FieldCompletionStatus.MISSING)

        percentage = int((completed_fields / total_fields) * 100) if total_fields > 0 else 0

        # Determine overall status
        if completed_fields == total_fields:
            status = ProgressStatus.COMPLETED
        elif missing_fields == total_fields:
            status = ProgressStatus.INITIAL
        else:
            status = ProgressStatus.IN_PROGRESS

        return ProgressInfo(
            total=total_fields,
            completed=completed_fields,
            pending_confirmation=pending_fields,
            missing=missing_fields,
            percentage=percentage,
            status=status,
        )

    def _get_all_fields_complete_msg(self, field_status: dict[RequiredField, FieldStatusInfo]) -> str:
        """Get a summary of all field statuses."""
        summary_parts = []

        for field in self._FIELD_COLLECTION_ORDER:
            field_info = field_status.get(field)
            if not field_info:
                continue

            if field_info.status == FieldCompletionStatus.COMPLETED:
                summary_parts.append(f'✅ {field_info.display_name}')
            elif field_info.status == FieldCompletionStatus.PENDING_CONFIRMATION:
                summary_parts.append(f'⏳ {field_info.display_name} (needs confirmation)')
            elif field_info.status == FieldCompletionStatus.MISSING:
                summary_parts.append(f'❌ {field_info.display_name} (missing)')
            else:
                raise NotImplementedError(f'Unknown field status: {field_info.status}')

        return ALL_FIELDS_COMPLETE_MESSAGE.format(field_summary='\n'.join(summary_parts))
